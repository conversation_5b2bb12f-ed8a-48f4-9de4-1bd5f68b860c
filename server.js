const http = require('http');
const fs = require('fs').promises;
const path = require('path');
const url = require('url');
const sharp = require('sharp');

const PORT = process.env.PORT || 3000;
const CONTENT_DIR = path.join(__dirname, 'content');
const PUBLIC_DIR = path.join(__dirname, 'public');

const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);

    if (req.method === 'GET' && parsedUrl.pathname === '/api/browse') {
        const folderPath = parsedUrl.query.path || '';
        const offset = parseInt(parsedUrl.query.offset) || 0;
        const limit = parseInt(parsedUrl.query.limit) || 20;

        try {
            const fullPath = path.join(CONTENT_DIR, folderPath);

            // Security check - ensure path is within content directory
            const resolvedPath = path.resolve(fullPath);
            const resolvedContentDir = path.resolve(CONTENT_DIR);
            if (!resolvedPath.startsWith(resolvedContentDir)) {
                res.writeHead(403, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Access denied' }));
                return;
            }

            const items = await fs.readdir(fullPath, { withFileTypes: true });

            // Separate folders and images
            const folders = items
                .filter(item => item.isDirectory())
                .map(item => ({
                    name: item.name,
                    displayName: item.name.replace(/_/g, ' '),
                    type: 'folder'
                }))
                .sort((a, b) => a.displayName.localeCompare(b.displayName));

            const imageFiles = items
                .filter(item => item.isFile() && /\.(png|jpe?g|gif|bmp|webp)$/i.test(item.name))
                .map(item => item.name)
                .sort((a, b) => {
                    // Extract timestamp from filename (format: YYYYMMDD_HHMM_*)
                    const getTimestamp = (filename) => {
                        const match = filename.match(/^(\d{8}_\d{4})/);
                        return match ? match[1] : '00000000_0000';
                    };

                    const timestampA = getTimestamp(a);
                    const timestampB = getTimestamp(b);

                    // Sort in descending order (newest first)
                    return timestampB.localeCompare(timestampA);
                });

            const totalImages = imageFiles.length;
            const imageSlice = imageFiles.slice(offset, offset + limit);

            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({
                folders,
                images: imageSlice,
                total: totalImages,
                currentPath: folderPath
            }));
        } catch (err) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Unable to browse directory' }));
        }
        return;
    }

    if (req.method === 'DELETE' && parsedUrl.pathname.startsWith('/api/images/')) {
        const imagePath = decodeURIComponent(parsedUrl.pathname.replace('/api/images/', ''));
        const filePath = path.join(CONTENT_DIR, imagePath);
        try {
            await fs.unlink(filePath);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (err) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Failed to delete image' }));
        }
        return;
    }

    if (req.method === 'POST' && parsedUrl.pathname.startsWith('/api/whatsapp-style/')) {
        const imagePath = decodeURIComponent(parsedUrl.pathname.replace('/api/whatsapp-style/', ''));
        const inputPath = path.join(CONTENT_DIR, imagePath);

        try {
            // Check if input file exists
            await fs.access(inputPath);

            // Process image using Sharp (same logic as whatsapp_style_single.js)
            const width = 1920;
            const height = 1080;
            const blur = 15;

            // Background: cover + blur
            const bgBuffer = await sharp(inputPath)
                .rotate() // honor EXIF orientation
                .resize({ width, height, fit: 'cover' })
                .blur(blur)
                .toBuffer();

            // Foreground: fit inside (no upscaling)
            const fg = sharp(inputPath)
                .rotate()
                .resize({ width, height, fit: 'inside', withoutEnlargement: true });
            const fgBuffer = await fg.toBuffer();
            const fgMeta = await sharp(fgBuffer).metadata();
            const fgW = fgMeta.width || width;
            const fgH = fgMeta.height || height;

            const left = Math.floor((width - fgW) / 2);
            const top = Math.floor((height - fgH) / 2);

            // Composite
            let canvas = sharp(bgBuffer).composite([{ input: fgBuffer, left, top }]);

            // Format-specific options
            const ext = path.extname(imagePath).toLowerCase();
            if (ext === '.jpg' || ext === '.jpeg') {
                canvas = canvas.jpeg({ quality: 95, mozjpeg: true });
            } else if (ext === '.png') {
                canvas = canvas.png();
            } else if (ext === '.webp') {
                canvas = canvas.webp({ quality: 95 });
            } else if (ext === '.tif' || ext === '.tiff') {
                canvas = canvas.tiff({ quality: 95 });
            }

            const outputBuffer = await canvas.toBuffer();

            // Set headers for download
            const filename = path.basename(imagePath);
            const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
            const outputExt = ['jpg', 'jpeg', 'png', 'webp'].includes(ext.slice(1)) ? ext.slice(1) : 'png';
            const outputFilename = `${nameWithoutExt}_whatsapp_style.${outputExt}`;

            res.writeHead(200, {
                'Content-Type': getContentType(ext),
                'Content-Disposition': `attachment; filename="${outputFilename}"`,
                'Content-Length': outputBuffer.length
            });
            res.end(outputBuffer);

        } catch (err) {
            console.error('WhatsApp style processing error:', err);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Failed to process image' }));
        }
        return;
    }

    if (parsedUrl.pathname.startsWith('/images/')) {
        const imagePath = decodeURIComponent(parsedUrl.pathname.replace('/images/', ''));
        const filePath = path.join(CONTENT_DIR, imagePath);

        // Security check - ensure path is within content directory
        const resolvedPath = path.resolve(filePath);
        const resolvedContentDir = path.resolve(CONTENT_DIR);
        if (!resolvedPath.startsWith(resolvedContentDir)) {
            res.writeHead(403);
            res.end('Access denied');
            return;
        }

        return serveFile(res, filePath);
    }

    let filePath = path.join(PUBLIC_DIR, parsedUrl.pathname === '/' ? 'index.html' : parsedUrl.pathname);
    serveFile(res, filePath);
});

function serveFile(res, filePath) {
    fs.readFile(filePath).then(data => {
        const ext = path.extname(filePath).toLowerCase();
        res.writeHead(200, { 'Content-Type': getContentType(ext) });
        res.end(data);
    }).catch(() => {
        res.writeHead(404);
        res.end('Not found');
    });
}

function getContentType(ext) {
    switch (ext) {
        case '.html': return 'text/html';
        case '.css': return 'text/css';
        case '.js': return 'application/javascript';
        case '.png': return 'image/png';
        case '.jpg':
        case '.jpeg': return 'image/jpeg';
        case '.gif': return 'image/gif';
        default: return 'application/octet-stream';
    }
}

server.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}`);
});

let offset = 0;
const limit = 20;
let totalCount = 0;

async function loadImages() {
    const res = await fetch(`/api/images?offset=${offset}&limit=${limit}`);
    const data = await res.json();
    totalCount = data.total;
    document.getElementById('imageCount').textContent = `Total images: ${totalCount}`;
    const gallery = document.getElementById('gallery');
    data.images.forEach(name => {
        const wrapper = document.createElement('div');
        wrapper.className = 'img-wrapper';

        const img = document.createElement('img');
        img.src = `/images/${name}`;
        img.alt = name;
        wrapper.appendChild(img);

        const buttons = document.createElement('div');
        buttons.className = 'buttons';

        const delBtn = document.createElement('button');
        delBtn.textContent = 'Delete';
        delBtn.onclick = async () => {
            if (confirm('Delete this image?')) {
                await fetch(`/api/images/${encodeURIComponent(name)}`, { method: 'DELETE' });
                wrapper.remove();
                totalCount--;
                document.getElementById('imageCount').textContent = `Total images: ${totalCount}`;
            }
        };

        const dlLink = document.createElement('a');
        dlLink.textContent = 'Download';
        dlLink.href = `/images/${name}`;
        dlLink.download = name;

        buttons.appendChild(delBtn);
        buttons.appendChild(dlLink);
        wrapper.appendChild(buttons);

        const filename = document.createElement('div');
        filename.className = 'filename';
        filename.textContent = name;
        wrapper.appendChild(filename);

        gallery.appendChild(wrapper);
    });
    offset += data.images.length;
    if (data.images.length < limit) {
        document.getElementById('loadMore').style.display = 'none';
    }
}

document.getElementById('loadMore').addEventListener('click', loadImages);
window.addEventListener('DOMContentLoaded', loadImages);

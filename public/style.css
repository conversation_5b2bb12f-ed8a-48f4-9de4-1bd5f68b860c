body {
    font-family: Arial, sans-serif;
    margin: 0 auto;
    padding: 20px;
    max-width: 1920px;
}

#imageCount {
    margin-bottom: 10px;
    font-weight: bold;
}

#gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 10px;
    border: 2px solid #333;
    padding: 10px;
    background: #f9f9f9;
}

.img-wrapper {
    position: relative;
    border: 1px solid #ccc;
}

.img-wrapper img {
    width: 100%;
    height: auto;
    display: block;
}

.buttons {
    position: absolute;
    top: 5px;
    right: 5px;
    display: none;
    gap: 5px;
}

.img-wrapper:hover .buttons {
    display: flex;
}

.buttons button,
.buttons a {
    background: rgba(0,0,0,0.7);
    border: 1px solid #fff;
    padding: 5px 8px;
    text-decoration: none;
    color: #fff;
    cursor: pointer;
    font-size: 12px;
    border-radius: 3px;
    display: inline-block;
}

#loadMore {
    margin-top: 20px;
    padding: 10px 20px;
    border: 1px solid #333;
    background-color: #007bff;
    color: #fff;
    cursor: pointer;
    border-radius: 4px;
}

.filename {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: rgba(0,0,0,0.5);
    color: #fff;
    text-align: center;
    font-size: 14px;
    padding: 2px 0;
}

# Simple Image Gallery

A minimal web application that serves images from `content/generations` in a two-column grid. It loads images in batches of twenty and provides delete and download controls for each image.

## Usage

1. Add your images to the `content/generations` directory.
2. Start the server:

   ```
   npm start
   ```
3. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Features

- Displays images in a responsive two-column layout.
- Shows image filenames as overlays.
- Delete or download images directly from the gallery.
- "Load more" button fetches the next batch of images.

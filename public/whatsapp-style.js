// WhatsApp Style Image Processor - Server-side processing with <PERSON>
// Creates a blurred background with the original image centered using high-quality Sharp library

class WhatsAppStyleProcessor {
    async processImage(fullImagePath, filename) {
        try {
            console.log('Starting WhatsApp style processing for:', filename);

            // Make request to server endpoint for processing
            const response = await fetch(`/api/whatsapp-style/${encodeURIComponent(fullImagePath)}`, {
                method: 'POST'
            });

            if (!response.ok) {
                throw new Error(`Server error: ${response.status}`);
            }

            // Get the processed image as blob
            const blob = await response.blob();
            console.log('Image processed successfully on server');

            // Create download link
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            // Get filename from Content-Disposition header or generate one
            const contentDisposition = response.headers.get('Content-Disposition');
            let downloadFilename;
            if (contentDisposition && contentDisposition.includes('filename=')) {
                downloadFilename = contentDisposition.split('filename=')[1].replace(/"/g, '');
            } else {
                const ext = filename.split('.').pop().toLowerCase();
                const nameWithoutExt = filename.replace(/\.[^/.]+$/, '');
                downloadFilename = `${nameWithoutExt}_whatsapp_style.${ext}`;
            }

            a.download = downloadFilename;

            // Trigger download
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // Clean up
            URL.revokeObjectURL(url);
            console.log('Download completed:', downloadFilename);

        } catch (error) {
            console.error('Error processing image:', error);
            alert('Error processing image: ' + error.message);
        }
    }

}

// Global instance
window.whatsappProcessor = new WhatsAppStyleProcessor();

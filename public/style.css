* {
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1920px;
    margin: 0 auto;
    padding: 20px;
}

h1 {
    text-align: center;
    color: #fff;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-weight: 300;
    letter-spacing: 2px;
}

#breadcrumb {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1rem;
    color: #fff;
}

.breadcrumb-item {
    display: inline-block;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 15px;
    transition: all 0.2s ease;
}

.breadcrumb-item:hover {
    background: rgba(255,255,255,0.1);
    color: #fff;
}

.breadcrumb-item.current {
    color: #fff;
    font-weight: 600;
}

.breadcrumb-separator {
    margin: 0 8px;
    color: rgba(255,255,255,0.6);
}

#folderView {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.folder-card {
    background: rgba(255,255,255,0.95);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border: 1px solid rgba(255,255,255,0.3);
}

.folder-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    background: rgba(255,255,255,1);
}

.folder-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    color: #667eea;
}

.folder-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.folder-count {
    font-size: 0.9rem;
    color: #666;
}

.folder-card.symlink {
    border-left: 4px solid #667eea;
}

.folder-card.symlink .folder-icon::after {
    content: '🔗';
    font-size: 1rem;
    position: absolute;
    margin-left: -10px;
    margin-top: -5px;
}

#imageCount {
    text-align: center;
    margin-bottom: 30px;
    font-weight: 600;
    font-size: 1.2rem;
    color: #fff;
    background: rgba(255,255,255,0.1);
    padding: 15px 30px;
    border-radius: 50px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    display: inline-block;
    margin-left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

#gallery {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 25px;
    padding: 30px;
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.3);
}

.img-wrapper {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255,255,255,0.2);
}

.img-wrapper:hover {
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.img-wrapper img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
}

.img-wrapper:hover img {
    transform: scale(1.05);
}

.buttons {
    position: absolute;
    top: 15px;
    right: 15px;
    display: none;
    gap: 6px;
    z-index: 10;
    flex-wrap: wrap;
    max-width: 200px;
}

.img-wrapper:hover .buttons {
    display: flex;
    animation: fadeInButtons 0.3s ease;
}

@keyframes fadeInButtons {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.buttons button,
.buttons a {
    background: rgba(0,0,0,0.8);
    border: none;
    padding: 6px 10px;
    text-decoration: none;
    color: #fff;
    cursor: pointer;
    font-size: 11px;
    border-radius: 6px;
    display: inline-block;
    font-weight: 500;
    transition: all 0.2s ease;
    backdrop-filter: blur(10px);
    white-space: nowrap;
    min-width: 60px;
    text-align: center;
}

.buttons button:hover,
.buttons a:hover {
    background: rgba(255,255,255,0.9);
    color: #333;
    transform: scale(1.05);
}

.buttons button:disabled {
    background: rgba(0,0,0,0.5);
    cursor: not-allowed;
    transform: none;
    opacity: 0.7;
}

.buttons button:disabled:hover {
    background: rgba(0,0,0,0.5);
    color: #fff;
    transform: none;
}

/* Processing animation */
.buttons button.processing {
    background: linear-gradient(45deg, #667eea, #764ba2);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
    100% {
        opacity: 1;
    }
}

#loadMore {
    display: block;
    margin: 40px auto 0;
    padding: 15px 40px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    cursor: pointer;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    text-transform: uppercase;
    letter-spacing: 1px;
}

#loadMore:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

#loadMore:active {
    transform: translateY(0);
}

.filename {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    color: #fff;
    text-align: center;
    font-size: 13px;
    padding: 15px 10px 10px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    h1 {
        font-size: 2rem;
        margin-bottom: 20px;
    }

    #gallery {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 20px;
    }

    #imageCount {
        font-size: 1rem;
        padding: 12px 25px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    h1 {
        font-size: 1.5rem;
        letter-spacing: 1px;
    }

    #gallery {
        padding: 15px;
        gap: 15px;
    }

    .buttons {
        top: 10px;
        right: 10px;
        gap: 5px;
    }

    .buttons button,
    .buttons a {
        padding: 6px 10px;
        font-size: 11px;
    }
}

/* Large screens optimization */
@media (min-width: 1400px) {
    #gallery {
        gap: 30px;
        padding: 40px;
    }

    h1 {
        font-size: 3rem;
    }
}

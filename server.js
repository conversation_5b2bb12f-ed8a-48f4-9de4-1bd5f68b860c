const http = require('http');
const fs = require('fs').promises;
const path = require('path');
const url = require('url');

const PORT = process.env.PORT || 3000;
const IMG_DIR = path.join(__dirname, 'content/generations');
const PUBLIC_DIR = path.join(__dirname, 'public');

const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);

    if (req.method === 'GET' && parsedUrl.pathname === '/api/images') {
        const offset = parseInt(parsedUrl.query.offset) || 0;
        const limit = parseInt(parsedUrl.query.limit) || 20;
        try {
            let files = await fs.readdir(IMG_DIR);
            files = files.filter(f => /\.(png|jpe?g|gif|bmp)$/i.test(f)).sort();
            const total = files.length;
            const slice = files.slice(offset, offset + limit);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ images: slice, total }));
        } catch (err) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Unable to read images' }));
        }
        return;
    }

    if (req.method === 'DELETE' && parsedUrl.pathname.startsWith('/api/images/')) {
        const name = decodeURIComponent(parsedUrl.pathname.replace('/api/images/', ''));
        const filePath = path.join(IMG_DIR, name);
        try {
            await fs.unlink(filePath);
            res.writeHead(200, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ success: true }));
        } catch (err) {
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Failed to delete image' }));
        }
        return;
    }

    if (parsedUrl.pathname.startsWith('/images/')) {
        const name = decodeURIComponent(parsedUrl.pathname.replace('/images/', ''));
        const filePath = path.join(IMG_DIR, name);
        return serveFile(res, filePath);
    }

    let filePath = path.join(PUBLIC_DIR, parsedUrl.pathname === '/' ? 'index.html' : parsedUrl.pathname);
    serveFile(res, filePath);
});

function serveFile(res, filePath) {
    fs.readFile(filePath).then(data => {
        const ext = path.extname(filePath).toLowerCase();
        res.writeHead(200, { 'Content-Type': getContentType(ext) });
        res.end(data);
    }).catch(() => {
        res.writeHead(404);
        res.end('Not found');
    });
}

function getContentType(ext) {
    switch (ext) {
        case '.html': return 'text/html';
        case '.css': return 'text/css';
        case '.js': return 'application/javascript';
        case '.png': return 'image/png';
        case '.jpg':
        case '.jpeg': return 'image/jpeg';
        case '.gif': return 'image/gif';
        default: return 'application/octet-stream';
    }
}

server.listen(PORT, () => {
    console.log(`Server running at http://localhost:${PORT}`);
});

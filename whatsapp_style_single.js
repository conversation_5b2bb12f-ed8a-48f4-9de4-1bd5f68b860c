// whatsapp_style_single.js
//
// Usage:
//   node whatsapp_style_single.js input.png
// Options:
//   node whatsapp_style_single.js input.png --width 1920 --height 1080 --blur 25 --output output/ --overwrite
//
// Deps:
//   npm i sharp
//
// Notes:
// - Supports .jpg/.jpeg/.png/.webp/.bmp/.tiff
// - Honors EXIF orientation via sharp().rotate()

const fs = require('fs/promises');
const path = require('path');
const sharp = require('sharp');

function parseArgs() {
  const args = process.argv.slice(2);
  if (args.length === 0) {
    console.error('Usage: node whatsapp_style_single.js <input_file> [--width 1920] [--height 1080] [--blur 25] [--output output/] [--overwrite]');
    process.exit(1);
  }
  const opts = {
    inputFile: args[0],
    width: 1920,
    height: 1080,
    blur: 15,
    outputDir: 'output',
    overwrite: false
  };
  for (let i = 1; i < args.length; i++) {
    const a = args[i];
    if (a === '--overwrite') { opts.overwrite = true; continue; }
    const next = args[i + 1];
    if (a === '--width' && next) { opts.width = parseInt(next, 10); i++; continue; }
    if (a === '--height' && next) { opts.height = parseInt(next, 10); i++; continue; }
    if (a === '--blur' && next) { opts.blur = parseFloat(next); i++; continue; }
    if (a === '--output' && next) { opts.outputDir = next; i++; continue; }
  }
  return opts;
}

async function ensureDir(p) {
  await fs.mkdir(p, { recursive: true });
}

async function processOne(inputPath, opts) {
  const { width, height, blur, outputDir, overwrite } = opts;
  const inFileName = path.basename(inputPath);
  const outPath = path.join(outputDir, inFileName);

  if (!overwrite) {
    try {
      await fs.access(outPath);
      console.log(`[skip] ${outPath} (exists)`);
      return;
    } catch {}
  }

  await ensureDir(outputDir);

  // Background: cover + blur
  const bgBuffer = await sharp(inputPath)
    .rotate() // honor EXIF orientation
    .resize({ width, height, fit: 'cover' })
    .blur(blur)
    .toBuffer();

  // Foreground: fit inside (no upscaling)
  const fg = sharp(inputPath)
    .rotate()
    .resize({ width, height, fit: 'inside', withoutEnlargement: true });
  const fgBuffer = await fg.toBuffer();
  const fgMeta = await sharp(fgBuffer).metadata();
  const fgW = fgMeta.width || width;
  const fgH = fgMeta.height || height;

  const left = Math.floor((width - fgW) / 2);
  const top = Math.floor((height - fgH) / 2);

  // Composite
  let canvas = sharp(bgBuffer).composite([{ input: fgBuffer, left, top }]);

  // Format-specific options
  const ext = path.extname(inFileName).toLowerCase();
  if (ext === '.jpg' || ext === '.jpeg') {
    canvas = canvas.jpeg({ quality: 95, mozjpeg: true });
  } else if (ext === '.png') {
    canvas = canvas.png();
  } else if (ext === '.webp') {
    canvas = canvas.webp({ quality: 95 });
  } else if (ext === '.tif' || ext === '.tiff') {
    canvas = canvas.tiff({ quality: 95 });
  }

  await canvas.toFile(outPath);
  console.log(`[ok] Saved: ${outPath}`);
}

(async function main() {
  const opts = parseArgs();
  const inputPath = path.resolve(opts.inputFile);

  try {
    const st = await fs.stat(inputPath);
    if (!st.isFile()) throw new Error('Not a file');
  } catch {
    console.error(`Input file not found: ${inputPath}`);
    process.exit(1);
  }

  await processOne(inputPath, opts);
})();

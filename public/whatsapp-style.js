// WhatsApp Style Image Processor for Browser
// Creates a blurred background with the original image centered

class WhatsAppStyleProcessor {
    constructor() {
        this.canvas = document.createElement('canvas');
        this.ctx = this.canvas.getContext('2d');
        this.width = 1920;
        this.height = 1080;
        this.blurAmount = 15;
    }

    async processImage(imageUrl, filename) {
        try {
            console.log('Starting WhatsApp style processing for:', filename);

            // Load the image
            const img = await this.loadImage(imageUrl);
            console.log('Image loaded successfully:', img.width, 'x', img.height);

            // Set canvas dimensions
            this.canvas.width = this.width;
            this.canvas.height = this.height;

            // Create blurred background
            await this.createBlurredBackground(img);
            console.log('Blurred background created');

            // Add centered original image
            this.addCenteredImage(img);
            console.log('Centered image added');

            // Download the result
            this.downloadImage(filename);
            console.log('Download initiated');

        } catch (error) {
            console.error('Error processing image:', error);
            alert('Error processing image: ' + error.message);
        }
    }

    loadImage(src) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            // Don't set crossOrigin for same-origin requests
            if (src.startsWith('http') && !src.startsWith(window.location.origin)) {
                img.crossOrigin = 'anonymous';
            }
            img.onload = () => {
                console.log('Image loaded:', src);
                resolve(img);
            };
            img.onerror = (error) => {
                console.error('Image load error:', error);
                reject(new Error('Failed to load image'));
            };
            img.src = src;
        });
    }

    async createBlurredBackground(img) {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.width, this.height);

        // Calculate dimensions for cover fit
        const imgAspect = img.width / img.height;
        const canvasAspect = this.width / this.height;

        let drawWidth, drawHeight, offsetX, offsetY;

        if (imgAspect > canvasAspect) {
            // Image is wider than canvas
            drawHeight = this.height;
            drawWidth = drawHeight * imgAspect;
            offsetX = (this.width - drawWidth) / 2;
            offsetY = 0;
        } else {
            // Image is taller than canvas
            drawWidth = this.width;
            drawHeight = drawWidth / imgAspect;
            offsetX = 0;
            offsetY = (this.height - drawHeight) / 2;
        }

        // Draw the background image (cover fit)
        this.ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);

        // Apply a simple blur effect by scaling down and up
        const imageData = this.ctx.getImageData(0, 0, this.width, this.height);

        // Create a smaller version for blur effect
        const smallCanvas = document.createElement('canvas');
        const smallCtx = smallCanvas.getContext('2d');
        const scale = 0.1; // Scale down to 10% for blur effect
        smallCanvas.width = this.width * scale;
        smallCanvas.height = this.height * scale;

        // Draw scaled down version
        smallCtx.drawImage(this.canvas, 0, 0, smallCanvas.width, smallCanvas.height);

        // Clear main canvas and draw scaled up version (creates blur effect)
        this.ctx.clearRect(0, 0, this.width, this.height);
        this.ctx.imageSmoothingEnabled = true;
        this.ctx.drawImage(smallCanvas, 0, 0, this.width, this.height);
    }

    addCenteredImage(img) {
        // Calculate dimensions for inside fit (no upscaling)
        const imgAspect = img.width / img.height;
        const canvasAspect = this.width / this.height;
        
        let drawWidth, drawHeight;
        
        if (imgAspect > canvasAspect) {
            // Image is wider - fit to width
            drawWidth = Math.min(img.width, this.width);
            drawHeight = drawWidth / imgAspect;
        } else {
            // Image is taller - fit to height
            drawHeight = Math.min(img.height, this.height);
            drawWidth = drawHeight * imgAspect;
        }
        
        // Center the image
        const offsetX = (this.width - drawWidth) / 2;
        const offsetY = (this.height - drawHeight) / 2;
        
        // Draw the centered original image
        this.ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);
    }

    downloadImage(originalFilename) {
        // Create download link
        this.canvas.toBlob((blob) => {
            if (!blob) {
                console.error('Failed to create blob');
                alert('Failed to create image. Please try again.');
                return;
            }

            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            // Generate filename
            const ext = originalFilename.split('.').pop().toLowerCase();
            const nameWithoutExt = originalFilename.replace(/\.[^/.]+$/, '');

            // Use PNG for better quality, but keep original extension if it's a supported format
            const outputExt = ['jpg', 'jpeg', 'png', 'webp'].includes(ext) ? ext : 'png';
            a.download = `${nameWithoutExt}_whatsapp_style.${outputExt}`;

            // Trigger download
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);

            // Clean up
            URL.revokeObjectURL(url);
            console.log('Download completed:', a.download);
        }, `image/${['jpg', 'jpeg'].includes(originalFilename.split('.').pop().toLowerCase()) ? 'jpeg' : 'png'}`, 0.95);
    }
}

// Global instance
window.whatsappProcessor = new WhatsAppStyleProcessor();

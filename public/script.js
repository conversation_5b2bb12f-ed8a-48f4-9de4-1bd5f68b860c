let offset = 0;
const limit = 20;
let totalCount = 0;
let loadedCount = 0;
let currentPath = '';

async function loadContent(folderPath = '') {
    try {
        const res = await fetch(`/api/browse?path=${encodeURIComponent(folderPath)}&offset=${offset}&limit=${limit}`);
        const data = await res.json();

        currentPath = folderPath;
        totalCount = data.total;

        // Update breadcrumb
        updateBreadcrumb(folderPath);

        // Show folders if any
        displayFolders(data.folders);

        // Show images if any
        if (data.images.length > 0) {
            displayImages(data.images, folderPath);
        } else if (offset === 0) {
            // Clear gallery if no images and this is the first load
            document.getElementById('gallery').innerHTML = '';
        }

        updateImageCount();
        updateLoadMoreButton(data.images.length);

    } catch (error) {
        console.error('Error loading content:', error);
        alert('Error loading content. Please try again.');
    }
}

function updateBreadcrumb(folderPath) {
    const breadcrumb = document.getElementById('breadcrumb');
    const parts = folderPath ? folderPath.split('/').filter(p => p) : [];

    let html = '<a href="#" class="breadcrumb-item" onclick="navigateToFolder(\'\')">🏠 Home</a>';

    let currentPath = '';
    parts.forEach((part, index) => {
        currentPath += (currentPath ? '/' : '') + part;
        const displayName = part.replace(/_/g, ' ');
        html += '<span class="breadcrumb-separator">›</span>';

        if (index === parts.length - 1) {
            html += `<span class="breadcrumb-item current">${displayName}</span>`;
        } else {
            html += `<a href="#" class="breadcrumb-item" onclick="navigateToFolder('${currentPath}')">${displayName}</a>`;
        }
    });

    breadcrumb.innerHTML = html;
}

function displayFolders(folders) {
    const folderView = document.getElementById('folderView');

    if (folders.length === 0) {
        folderView.innerHTML = '';
        return;
    }

    let html = '';
    folders.forEach(folder => {
        const folderPath = currentPath ? `${currentPath}/${folder.name}` : folder.name;
        html += `
            <div class="folder-card" onclick="navigateToFolder('${folderPath}')">
                <div class="folder-icon">📁</div>
                <div class="folder-name">${folder.displayName}</div>
                <div class="folder-count">Click to explore</div>
            </div>
        `;
    });

    folderView.innerHTML = html;
}

async function navigateToFolder(folderPath) {
    // Reset pagination when navigating
    offset = 0;
    loadedCount = 0;

    // Clear current content
    document.getElementById('gallery').innerHTML = '';
    document.getElementById('folderView').innerHTML = '';

    // Load new content
    await loadContent(folderPath);
}

function displayImages(images, folderPath) {
    const gallery = document.getElementById('gallery');
    images.forEach(name => {
        const wrapper = document.createElement('div');
        wrapper.className = 'img-wrapper';

        const fullImagePath = folderPath ? `${folderPath}/${name}` : name;
        const img = document.createElement('img');
        img.src = `/images/${fullImagePath}`;
        img.alt = name;
        wrapper.appendChild(img);

        const buttons = document.createElement('div');
        buttons.className = 'buttons';

        const delBtn = document.createElement('button');
        delBtn.textContent = 'Delete';
        delBtn.onclick = async () => {
            if (confirm('Delete this image?')) {
                await fetch(`/api/images/${encodeURIComponent(fullImagePath)}`, { method: 'DELETE' });
                wrapper.remove();
                totalCount--;
                loadedCount--;
                updateImageCount();
            }
        };

        const dlLink = document.createElement('a');
        dlLink.textContent = 'Download';
        dlLink.href = `/images/${fullImagePath}`;
        dlLink.download = name;

        const whatsappBtn = document.createElement('button');
        whatsappBtn.textContent = 'WhatsApp Style';
        whatsappBtn.onclick = async () => {
            try {
                whatsappBtn.textContent = 'Processing...';
                whatsappBtn.disabled = true;
                whatsappBtn.classList.add('processing');
                await window.whatsappProcessor.processImage(fullImagePath, name);
                whatsappBtn.textContent = 'WhatsApp Style';
                whatsappBtn.disabled = false;
                whatsappBtn.classList.remove('processing');
            } catch (error) {
                console.error('WhatsApp processing error:', error);
                whatsappBtn.textContent = 'WhatsApp Style';
                whatsappBtn.disabled = false;
                whatsappBtn.classList.remove('processing');
                alert('Error processing image. Please try again.');
            }
        };

        buttons.appendChild(delBtn);
        buttons.appendChild(dlLink);
        buttons.appendChild(whatsappBtn);
        wrapper.appendChild(buttons);

        const filename = document.createElement('div');
        filename.className = 'filename';
        filename.textContent = name;
        wrapper.appendChild(filename);

        gallery.appendChild(wrapper);
    });

    loadedCount += images.length;
    offset += images.length;
}

function updateLoadMoreButton(imagesLoaded) {
    const loadMoreBtn = document.getElementById('loadMore');
    if (imagesLoaded < limit || loadedCount >= totalCount) {
        loadMoreBtn.style.display = 'none';
    } else {
        loadMoreBtn.style.display = 'block';
    }
}

function updateImageCount() {
    const imageCountElement = document.getElementById('imageCount');
    if (totalCount === 0) {
        imageCountElement.style.display = 'none';
    } else {
        imageCountElement.style.display = 'block';
        if (loadedCount === totalCount) {
            imageCountElement.textContent = `Showing all ${totalCount} images`;
        } else {
            imageCountElement.textContent = `Showing ${loadedCount} of ${totalCount} images`;
        }
    }
}

async function loadMoreImages() {
    await loadContent(currentPath);
}

document.getElementById('loadMore').addEventListener('click', loadMoreImages);
window.addEventListener('DOMContentLoaded', () => loadContent(''));

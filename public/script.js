let offset = 0;
const limit = 20;
let totalCount = 0;
let loadedCount = 0;

async function loadImages() {
    const res = await fetch(`/api/images?offset=${offset}&limit=${limit}`);
    const data = await res.json();
    totalCount = data.total;
    updateImageCount();
    const gallery = document.getElementById('gallery');
    data.images.forEach(name => {
        const wrapper = document.createElement('div');
        wrapper.className = 'img-wrapper';

        const img = document.createElement('img');
        img.src = `/images/${name}`;
        img.alt = name;
        wrapper.appendChild(img);

        const buttons = document.createElement('div');
        buttons.className = 'buttons';

        const delBtn = document.createElement('button');
        delBtn.textContent = 'Delete';
        delBtn.onclick = async () => {
            if (confirm('Delete this image?')) {
                await fetch(`/api/images/${encodeURIComponent(name)}`, { method: 'DELETE' });
                wrapper.remove();
                totalCount--;
                loadedCount--;
                updateImageCount();
            }
        };

        const dlLink = document.createElement('a');
        dlLink.textContent = 'Download';
        dlLink.href = `/images/${name}`;
        dlLink.download = name;

        const whatsappBtn = document.createElement('button');
        whatsappBtn.textContent = 'WhatsApp Style';
        whatsappBtn.onclick = async () => {
            try {
                whatsappBtn.textContent = 'Processing...';
                whatsappBtn.disabled = true;
                whatsappBtn.classList.add('processing');
                await window.whatsappProcessor.processImage(`/images/${name}`, name);
                whatsappBtn.textContent = 'WhatsApp Style';
                whatsappBtn.disabled = false;
                whatsappBtn.classList.remove('processing');
            } catch (error) {
                console.error('WhatsApp processing error:', error);
                whatsappBtn.textContent = 'WhatsApp Style';
                whatsappBtn.disabled = false;
                whatsappBtn.classList.remove('processing');
                alert('Error processing image. Please try again.');
            }
        };

        buttons.appendChild(delBtn);
        buttons.appendChild(dlLink);
        buttons.appendChild(whatsappBtn);
        wrapper.appendChild(buttons);

        const filename = document.createElement('div');
        filename.className = 'filename';
        filename.textContent = name;
        wrapper.appendChild(filename);

        gallery.appendChild(wrapper);
    });
    loadedCount += data.images.length;
    offset += data.images.length;
    updateImageCount();
    if (data.images.length < limit) {
        document.getElementById('loadMore').style.display = 'none';
    }
}

function updateImageCount() {
    const imageCountElement = document.getElementById('imageCount');
    if (loadedCount === totalCount) {
        imageCountElement.textContent = `Showing all ${totalCount} images`;
    } else {
        imageCountElement.textContent = `Showing ${loadedCount} of ${totalCount} images`;
    }
}

document.getElementById('loadMore').addEventListener('click', loadImages);
window.addEventListener('DOMContentLoaded', loadImages);
